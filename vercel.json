{"version": 2, "name": "officetech-guinea", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp|avif))", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/sw.js", "headers": {"Cache-Control": "no-cache"}}, {"src": "/(.*)", "dest": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://connect.facebook.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.convex.cloud https://clerk.com https://www.google-analytics.com; frame-src 'self' https://www.youtube.com;"}]}], "env": {"VITE_CLERK_PUBLISHABLE_KEY": "@vite_clerk_publishable_key", "VITE_CONVEX_URL": "@vite_convex_url"}, "build": {"env": {"VITE_CLERK_PUBLISHABLE_KEY": "@vite_clerk_publishable_key", "VITE_CONVEX_URL": "@vite_convex_url"}}}